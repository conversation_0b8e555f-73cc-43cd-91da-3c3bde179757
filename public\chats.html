<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Danh sách <PERSON></title>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
      }
      .entity-list {
        margin-top: 20px;
      }
      .entity-item {
        padding: 10px;
        border-bottom: 1px solid #ccc;
        display: flex;
        align-items: center;
      }
      .entity-item img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
      }
      .status-message {
        padding: 10px;
        margin-bottom: 10px;
      }
      .status-message.success {
        background-color: #dff0d8;
        color: #3c763d;
      }
      .status-message.error {
        background-color: #f2dede;
        color: #a94442;
      }
      .status-message.info {
        background-color: #d9edf7;
        color: #31708f;
      }
      .filter-container {
        margin-bottom: 10px;
      }
      .filter-container input {
        padding: 5px;
        width: 200px;
      }
      .template-container {
        margin-bottom: 10px;
      }
      .template-container select {
        padding: 5px;
        width: 200px;
      }
      .action-container {
        margin-bottom: 10px;
      }
      .action-container button {
        padding: 5px 10px;
        margin-right: 10px;
      }
    </style>
  </head>
  <body>
    <h1>Danh sách Chats</h1>
    <a href="/templates">Quản lý Mẫu Tin nhắn</a>
    <div class="filter-container">
      <input type="text" id="filter-input" placeholder="Tìm kiếm theo tên..." />
    </div>
    <div class="template-container">
      <select id="template-select">
        <option value="">Chọn mẫu tin nhắn</option>
      </select>
    </div>
    <div class="action-container">
      <button id="send-template-btn" disabled>Gửi Template</button>
      <button id="refresh-btn">Làm mới danh sách</button>
      <button id="logout-btn">Đăng xuất</button>
    </div>
    <div id="status-message" class="status-message" style="display: none"></div>
    <div id="entity-list" class="entity-list"></div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="chats.js"></script>
  </body>
</html>
