// Socket.io connection
const socket = io();

// DOM elements
const statusMessage = document.getElementById('status-message');
const templateList = document.getElementById('template-list');
const templateIdInput = document.getElementById('template-id');
const templateNameInput = document.getElementById('template-name');
const templateContentInput = document.getElementById('template-content');
const saveBtn = document.getElementById('save-btn');
const clearBtn = document.getElementById('clear-btn');

// State
let templates = [];
let isLoading = false;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    checkLoginStatus();
    setupEventListeners();
    fetchTemplates();
});

function setupEventListeners() {
    saveBtn.addEventListener('click', saveTemplate);
    clearBtn.addEventListener('click', clearForm);
    
    // Socket event listeners
    socket.on('connect', () => {
        console.log('Connected to server');
        fetchTemplates();
    });
    
    socket.on('status', handleStatusUpdate);
    socket.on('templates', handleTemplatesUpdate);
    
    socket.on('disconnect', () => {
        console.log('Disconnected from server');
        showStatus('Mất kết nối với server', 'error');
    });
    
    // Form validation
    templateNameInput.addEventListener('input', validateForm);
    templateContentInput.addEventListener('input', validateForm);
}

async function checkLoginStatus() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        if (data.status !== 'success') {
            // Redirect to login page if not logged in
            window.location.href = '/';
        }
    } catch (error) {
        console.error('Error checking login status:', error);
        window.location.href = '/';
    }
}

async function fetchTemplates() {
    if (isLoading) return;
    
    isLoading = true;
    showStatus('Đang tải danh sách mẫu tin nhắn...', 'info', true);
    
    try {
        const response = await fetch('/api/templates');
        const data = await response.json();
        
        if (data.success) {
            templates = data.templates || [];
            renderTemplates(templates);
            
            if (templates.length === 0) {
                showStatus('Chưa có mẫu tin nhắn nào', 'info');
            } else {
                showStatus(`Đã tải ${templates.length} mẫu tin nhắn`, 'success');
            }
        } else {
            showStatus(data.message, 'error');
        }
    } catch (error) {
        console.error('Error fetching templates:', error);
        showStatus('Lỗi khi tải danh sách mẫu tin nhắn', 'error');
    } finally {
        isLoading = false;
    }
}

async function saveTemplate() {
    const id = templateIdInput.value;
    const displayName = templateNameInput.value.trim();
    const content = templateContentInput.value.trim();
    
    if (!displayName || !content) {
        showStatus('Vui lòng nhập tên và nội dung mẫu tin nhắn', 'error');
        return;
    }
    
    const originalText = saveBtn.innerHTML;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="loading"></span> Đang lưu...';
    
    try {
        const url = id ? `/api/templates/${id}` : '/api/templates';
        const method = id ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ displayName, content }),
        });
        
        const data = await response.json();
        
        if (data.success) {
            showStatus(
                id ? 'Cập nhật mẫu tin nhắn thành công' : 'Tạo mẫu tin nhắn thành công',
                'success'
            );
            clearForm();
            fetchTemplates(); // Refresh the list
        } else {
            showStatus(data.message, 'error');
        }
    } catch (error) {
        console.error('Error saving template:', error);
        showStatus(`Lỗi: ${error.message}`, 'error');
    } finally {
        saveBtn.disabled = false;
        saveBtn.innerHTML = originalText;
    }
}

function clearForm() {
    templateIdInput.value = '';
    templateNameInput.value = '';
    templateContentInput.value = '';
    saveBtn.innerHTML = '<i class="fas fa-save"></i> Lưu';
    validateForm();
    showStatus('Đã xóa form', 'info');
}

function validateForm() {
    const hasName = templateNameInput.value.trim().length > 0;
    const hasContent = templateContentInput.value.trim().length > 0;
    saveBtn.disabled = !(hasName && hasContent);
}

function editTemplate(id, displayName, content) {
    templateIdInput.value = id;
    templateNameInput.value = displayName;
    templateContentInput.value = content;
    saveBtn.innerHTML = '<i class="fas fa-edit"></i> Cập nhật';
    validateForm();
    showStatus('Đang chỉnh sửa mẫu tin nhắn', 'info');
    
    // Scroll to form
    document.querySelector('.form-container').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
}

async function deleteTemplate(id) {
    if (!confirm('Bạn có chắc muốn xóa mẫu tin nhắn này?')) return;
    
    try {
        showStatus('Đang xóa mẫu tin nhắn...', 'info', true);
        
        const response = await fetch(`/api/templates/${id}`, {
            method: 'DELETE',
        });
        
        const data = await response.json();
        
        if (data.success) {
            showStatus('Xóa mẫu tin nhắn thành công', 'success');
            fetchTemplates(); // Refresh the list
        } else {
            showStatus(data.message, 'error');
        }
    } catch (error) {
        console.error('Error deleting template:', error);
        showStatus(`Lỗi khi xóa mẫu: ${error.message}`, 'error');
    }
}

function renderTemplates(templates) {
    const emptyState = document.getElementById('empty-state');
    
    templateList.innerHTML = '';
    
    if (templates.length === 0) {
        if (emptyState) {
            emptyState.style.display = 'block';
        } else {
            templateList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>Chưa có mẫu tin nhắn nào</h3>
                    <p>Hãy tạo mẫu tin nhắn đầu tiên của bạn</p>
                </div>
            `;
        }
        return;
    }
    
    if (emptyState) {
        emptyState.style.display = 'none';
    }
    
    templates.forEach((template) => {
        const templateItem = document.createElement('div');
        templateItem.className = 'template-item';
        
        const truncatedContent = template.content.length > 100 
            ? template.content.substring(0, 100) + '...' 
            : template.content;
        
        templateItem.innerHTML = `
            <div class="template-header">
                <div class="template-info">
                    <div class="template-name">${escapeHtml(template.displayName)}</div>
                    <div class="template-content">${escapeHtml(truncatedContent)}</div>
                    <div class="template-id">ID: ${template.id}</div>
                </div>
                <div class="template-actions">
                    <button class="btn btn-secondary btn-small" onclick="editTemplate('${template.id}', '${escapeHtml(template.displayName)}', '${escapeHtml(template.content)}')">
                        <i class="fas fa-edit"></i> Sửa
                    </button>
                    <button class="btn btn-danger btn-small" onclick="deleteTemplate('${template.id}')">
                        <i class="fas fa-trash"></i> Xóa
                    </button>
                </div>
            </div>
        `;
        
        templateList.appendChild(templateItem);
    });
}

function handleStatusUpdate(data) {
    console.log('Status update:', data);
    showStatus(data.message, data.status);
    if (data.redirect) {
        setTimeout(() => {
            window.location.href = data.redirect;
        }, 1000);
    }
}

function handleTemplatesUpdate(data) {
    console.log('Templates update:', data);
    templates = data.templates || [];
    renderTemplates(templates);
}

function showStatus(message, type = 'info', showLoading = false) {
    if (showLoading) {
        statusMessage.innerHTML = `<span class="loading"></span> ${message}`;
    } else {
        statusMessage.textContent = message;
    }
    statusMessage.className = `status-message ${type}`;
    statusMessage.style.display = 'block';
    
    // Auto hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            statusMessage.style.display = 'none';
        }, 3000);
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Make functions global for onclick handlers
window.editTemplate = editTemplate;
window.deleteTemplate = deleteTemplate;
